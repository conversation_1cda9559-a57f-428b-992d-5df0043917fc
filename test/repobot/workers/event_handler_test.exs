defmodule Repobot.Workers.EventHandlerTest do
  use Repobot.DataCase, async: true

  import Repobot.Test.Fixtures

  alias Repobot.Events

  # Test worker that uses EventHandler
  defmodule TestEventHandler do
    use Repobot.Workers.EventHandler

    @impl true
    def handle(%Events.Event{} = event) do
      # Store the event ID in the process dictionary for testing
      Process.put(:handled_event_id, event.id)
      :ok
    end
  end

  # Test worker that returns an error
  defmodule TestErrorEventHandler do
    use Repobot.Workers.EventHandler

    @impl true
    def handle(%Events.Event{} = _event) do
      {:error, "test error"}
    end
  end

  # Test worker that returns a result
  defmodule TestResultEventHandler do
    use Repobot.Workers.EventHandler

    @impl true
    def handle(%Events.Event{} = event) do
      {:ok, %{event_id: event.id, processed: true}}
    end
  end

  setup do
    organization = organization_fixture(%{name: "test-org"})
    %{organization: organization}
  end

  describe "EventHandler behavior" do
    test "successfully processes event and updates status", %{organization: organization} do
      # Create an event
      {:ok, event} =
        Events.create_event(%{
          type: "test.event",
          payload: %{"test" => "data"},
          organization_id: organization.id,
          status: "pending"
        })

      # Create and perform the job
      job = %Oban.Job{
        id: 1,
        worker: "TestEventHandler",
        queue: "default",
        args: %{"event_id" => event.id},
        attempt: 1,
        max_attempts: 3
      }

      # Perform the job
      result = TestEventHandler.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the event was handled
      assert Process.get(:handled_event_id) == event.id

      # Assert the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "handles event processing errors and updates status", %{organization: organization} do
      # Create an event
      {:ok, event} =
        Events.create_event(%{
          type: "test.error",
          payload: %{"test" => "data"},
          organization_id: organization.id,
          status: "pending"
        })

      # Create and perform the job
      job = %Oban.Job{
        id: 2,
        worker: "TestErrorEventHandler",
        queue: "default",
        args: %{"event_id" => event.id},
        attempt: 1,
        max_attempts: 3
      }

      # Perform the job
      result = TestErrorEventHandler.perform(job)

      # Assert the job failed
      assert result == {:error, "test error"}

      # Assert the event status was updated to failed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "failed"
    end

    test "handles event with result data", %{organization: organization} do
      # Create an event
      {:ok, event} =
        Events.create_event(%{
          type: "test.result",
          payload: %{"test" => "data"},
          organization_id: organization.id,
          status: "pending"
        })

      # Create and perform the job
      job = %Oban.Job{
        id: 3,
        worker: "TestResultEventHandler",
        queue: "default",
        args: %{"event_id" => event.id},
        attempt: 1,
        max_attempts: 3
      }

      # Perform the job
      result = TestResultEventHandler.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "handles missing event_id in job arguments" do
      job = %Oban.Job{
        id: 4,
        worker: "TestEventHandler",
        queue: "default",
        args: %{},
        attempt: 1,
        max_attempts: 3
      }

      result = TestEventHandler.perform(job)
      assert result == {:error, "Missing event_id in job arguments"}
    end

    test "handles non-existent event" do
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 5,
        worker: "TestEventHandler",
        queue: "default",
        args: %{"event_id" => non_existent_id},
        attempt: 1,
        max_attempts: 3
      }

      result = TestEventHandler.perform(job)
      assert result == {:error, "Event not found: #{non_existent_id}"}
    end
  end
end
